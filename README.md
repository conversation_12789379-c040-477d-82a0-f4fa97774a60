# Enhanced Turnstile Bypass Test

针对 Auth0 v2 + Cloudflare Turnstile 的绕过测试脚本

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行简单测试
```bash
python simple_bypass_test.py
```

### 3. 运行增强版测试
```bash
python enhanced_turnstile_bypass.py
```

## 📋 脚本说明

### simple_bypass_test.py
- **用途**: 快速测试基础绕过效果
- **特点**: 简单配置，快速启动
- **适用**: 初步测试网站是否有Turnstile验证

### enhanced_turnstile_bypass.py  
- **用途**: 完整的反检测测试
- **特点**: 多层反检测，Chrome扩展注入
- **适用**: 深度绕过测试

## 🔍 测试步骤

1. **运行脚本**: 选择合适的脚本运行
2. **观察浏览器**: 脚本会打开浏览器窗口
3. **检查页面**: 观察是否出现Turnstile验证框
4. **手动测试**: 尝试输入邮箱并点击继续
5. **观察结果**: 看是否触发验证或成功绕过

## 🛡️ 反检测技术

### 基础反检测
- 移除 `navigator.webdriver` 属性
- 禁用自动化控制特征
- 随机化窗口大小
- 伪造用户代理

### 增强反检测
- Chrome扩展注入
- WebGL指纹伪造
- Canvas指纹添加噪点
- 鼠标事件随机化
- 硬件信息伪造
- 权限查询伪造

## 📊 测试结果判断

### ✅ 绕过成功标志
- 页面正常加载，无验证框出现
- 能够正常输入邮箱地址
- 点击继续按钮不触发Turnstile
- 控制台无相关错误信息

### ❌ 绕过失败标志  
- 出现Turnstile验证框
- 页面卡在"检查浏览器"状态
- 控制台出现检测相关错误
- 无法正常操作页面元素

## ⚠️ 注意事项

1. **仅供测试**: 这些脚本仅用于技术研究和测试
2. **遵守法律**: 请遵守相关法律法规和网站服务条款
3. **风险自负**: 使用脚本可能导致账户被封等风险
4. **及时更新**: Cloudflare会不断更新检测机制

## 🔧 故障排除

### 常见问题

**Q: 浏览器启动失败**
A: 检查Chrome是否正确安装，尝试更新DrissionPage

**Q: 扩展加载失败** 
A: 确保有足够的磁盘空间创建临时文件

**Q: 页面加载缓慢**
A: 可能是网络问题或被限流，尝试使用代理

**Q: 仍然出现验证**
A: 当前检测机制可能已更新，需要改进反检测技术

## 📈 改进建议

如果当前脚本无法绕过，可以尝试：

1. **使用代理**: 添加高质量住宅代理
2. **TLS指纹**: 集成CycleTLS进行TLS指纹伪造  
3. **专业服务**: 使用CapSolver等验证码解决服务
4. **行为模拟**: 添加更多人类行为模拟
5. **指纹伪造**: 实现更深层的浏览器指纹伪造

## 📞 技术支持

如需更高级的绕过方案，建议：
- 研究最新的反检测技术
- 使用专业的验证码解决服务
- 结合多种绕过技术
- 定期更新反检测策略
