#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版 Cloudflare Turnstile 绕过脚本
针对 Auth0 v2 + Turnstile 的有头模式测试
"""

from DrissionPage import ChromiumOptions, Chromium
import os
import sys
import tempfile
import shutil
import random
import time
import json
from typing import Optional

# 增强版反检测脚本
ENHANCED_ANTI_DETECTION_SCRIPT = '''
(function() {
    'use strict';
    
    console.log('🚀 Enhanced Anti-Detection Script Loading...');
    
    // 1. 鼠标事件随机化
    function getRandomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
    
    let screenX = getRandomInt(800, 1400);
    let screenY = getRandomInt(400, 800);
    
    Object.defineProperty(MouseEvent.prototype, 'screenX', {
        get: function() { return screenX + getRandomInt(-5, 5); }
    });
    
    Object.defineProperty(MouseEvent.prototype, 'screenY', {
        get: function() { return screenY + getRandomInt(-5, 5); }
    });
    
    // 2. WebDriver 属性移除
    Object.defineProperty(navigator, 'webdriver', {
        get: () => undefined,
    });
    
    // 3. Chrome Runtime 清理
    if (window.chrome && window.chrome.runtime) {
        delete window.chrome.runtime.onConnect;
        delete window.chrome.runtime.onMessage;
    }
    
    // 4. WebGL 指纹伪造
    const getParameter = WebGLRenderingContext.prototype.getParameter;
    WebGLRenderingContext.prototype.getParameter = function(parameter) {
        if (parameter === 37445) return 'Intel Inc.';
        if (parameter === 37446) return 'Intel(R) UHD Graphics 630';
        return getParameter.call(this, parameter);
    };
    
    // 5. Canvas 指纹添加噪点
    const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
    HTMLCanvasElement.prototype.toDataURL = function() {
        const context = this.getContext('2d');
        if (context) {
            const imageData = context.getImageData(0, 0, this.width, this.height);
            for (let i = 0; i < imageData.data.length; i += 4) {
                imageData.data[i] += Math.floor(Math.random() * 3) - 1;
            }
            context.putImageData(imageData, 0, 0);
        }
        return originalToDataURL.apply(this, arguments);
    };
    
    // 6. 硬件信息伪造
    Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => 8
    });
    
    Object.defineProperty(navigator, 'deviceMemory', {
        get: () => 8
    });
    
    // 7. 语言和时区
    Object.defineProperty(navigator, 'languages', {
        get: () => ['en-US', 'en']
    });
    
    // 8. 插件信息伪造
    Object.defineProperty(navigator, 'plugins', {
        get: () => [
            {name: 'Chrome PDF Plugin', filename: 'internal-pdf-viewer'},
            {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
            {name: 'Native Client', filename: 'internal-nacl-plugin'}
        ]
    });
    
    // 9. 权限查询伪造
    if (navigator.permissions && navigator.permissions.query) {
        const originalQuery = navigator.permissions.query;
        navigator.permissions.query = function(permissionDesc) {
            return Promise.resolve({state: 'prompt'});
        };
    }
    
    // 10. 电池API移除
    if ('getBattery' in navigator) {
        delete navigator.getBattery;
    }
    
    console.log('✅ Enhanced Anti-Detection Script Loaded Successfully');
})();
'''

class EnhancedTurnstileBypass:
    def __init__(self):
        self.browser = None
        self._temp_extension_dir = None
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        ]
    
    def create_extension(self):
        """创建增强版Chrome扩展"""
        temp_dir = tempfile.mkdtemp(prefix="enhanced_turnstile_bypass_")
        self._temp_extension_dir = temp_dir
        
        # manifest.json
        manifest = {
            "manifest_version": 3,
            "name": "Enhanced Turnstile Bypass",
            "version": "3.0",
            "description": "Advanced anti-detection for Turnstile bypass",
            "content_scripts": [{
                "js": ["content.js"],
                "matches": ["<all_urls>"],
                "run_at": "document_start",
                "all_frames": True,
                "world": "MAIN"
            }],
            "permissions": ["storage", "activeTab"],
            "host_permissions": ["<all_urls>"]
        }
        
        with open(os.path.join(temp_dir, "manifest.json"), "w", encoding="utf-8") as f:
            json.dump(manifest, f, indent=2)
        
        # content.js
        with open(os.path.join(temp_dir, "content.js"), "w", encoding="utf-8") as f:
            f.write(ENHANCED_ANTI_DETECTION_SCRIPT)
        
        print(f"✅ 扩展创建成功: {temp_dir}")
        return temp_dir
    
    def init_browser(self, headless=False, proxy=None):
        """初始化增强版浏览器"""
        print("🚀 正在初始化增强版浏览器...")
        
        co = ChromiumOptions()
        
        # 创建并加载扩展
        extension_path = self.create_extension()
        co.add_extension(extension_path)
        
        # 基础反检测参数
        co.set_argument('--no-sandbox')
        co.set_argument('--disable-blink-features=AutomationControlled')
        co.set_argument('--disable-features=VizDisplayCompositor')
        co.set_argument('--disable-dev-shm-usage')
        co.set_argument('--disable-extensions-file-access-check')
        co.set_argument('--disable-extensions-http-throttling')
        co.set_argument('--disable-background-timer-throttling')
        co.set_argument('--disable-backgrounding-occluded-windows')
        co.set_argument('--disable-renderer-backgrounding')
        
        # 随机窗口大小
        width = random.randint(1200, 1920)
        height = random.randint(800, 1080)
        co.set_argument(f'--window-size={width},{height}')
        
        # 随机用户代理
        user_agent = random.choice(self.user_agents)
        co.set_user_agent(user_agent)
        
        # 代理设置
        if proxy:
            co.set_proxy(proxy)
            print(f"🌐 使用代理: {proxy}")
        
        # 无头模式
        if headless:
            co.headless()
        
        # 高级配置
        co.set_pref('webrtc.ip_handling_policy', 'disable_non_proxied_udp')
        co.set_pref('webrtc.multiple_routes', False)
        co.set_pref('webrtc.nonproxied_udp', False)
        co.set_pref('credentials_enable_service', False)
        co.set_pref('profile.password_manager_enabled', False)
        
        # 自动端口
        co.auto_port()
        
        try:
            self.browser = Chromium(co)
            print("✅ 浏览器初始化成功")
            
            # 等待扩展加载
            time.sleep(2)
            
            return self.browser
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {str(e)}")
            raise
    
    def human_like_delay(self, min_delay=1, max_delay=3):
        """人类行为延迟"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    def test_bypass(self, url="https://app.augmentcode.com/account"):
        """测试绕过效果"""
        print(f"🎯 开始测试绕过: {url}")
        
        try:
            # 初始化浏览器
            browser = self.init_browser(headless=False)
            
            print("📱 正在访问目标网站...")
            browser.get(url)
            
            # 等待页面加载
            self.human_like_delay(3, 5)
            
            # 检查页面标题
            title = browser.title
            print(f"📄 页面标题: {title}")
            
            # 检查是否有Turnstile元素
            turnstile_elements = browser.eles('iframe[src*="challenges.cloudflare.com"]')
            if turnstile_elements:
                print("🔍 检测到 Turnstile 验证框")
                for i, element in enumerate(turnstile_elements):
                    print(f"   Turnstile {i+1}: {element.attr('src')}")
            else:
                print("✅ 未检测到 Turnstile 验证框")
            
            # 检查控制台错误
            print("\n📊 页面分析:")
            print(f"   - URL: {browser.url}")
            print(f"   - 标题: {title}")
            
            # 尝试查找登录表单
            email_input = browser.ele('#username', timeout=2)
            if email_input:
                print("✅ 找到邮箱输入框")
                
                # 模拟人类输入
                print("⌨️  模拟输入邮箱...")
                test_email = "<EMAIL>"
                for char in test_email:
                    email_input.input(char)
                    time.sleep(random.uniform(0.05, 0.15))
                
                self.human_like_delay(1, 2)
                
                # 查找继续按钮
                continue_btn = browser.ele('button[type="submit"]', timeout=2)
                if continue_btn:
                    print("🔘 找到提交按钮")
                    print("⚠️  注意: 这里不会实际点击，避免触发验证")
                else:
                    print("❌ 未找到提交按钮")
            else:
                print("❌ 未找到邮箱输入框")
            
            # 保持浏览器打开一段时间供观察
            print("\n🔍 浏览器将保持打开60秒供您观察...")
            print("   请检查:")
            print("   1. 是否出现了 Turnstile 验证")
            print("   2. 页面是否正常加载")
            print("   3. 控制台是否有错误信息")
            
            time.sleep(60)
            
        except Exception as e:
            print(f"❌ 测试过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("🧹 正在清理资源...")
        
        if self.browser:
            try:
                self.browser.quit()
                print("✅ 浏览器已关闭")
            except:
                pass
        
        if self._temp_extension_dir and os.path.exists(self._temp_extension_dir):
            try:
                shutil.rmtree(self._temp_extension_dir)
                print("✅ 临时扩展目录已清理")
            except:
                pass

def main():
    """主函数"""
    print("🎯 Enhanced Turnstile Bypass Test")
    print("=" * 50)
    
    bypass_tool = EnhancedTurnstileBypass()
    
    try:
        # 测试绕过
        bypass_tool.test_bypass()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")
    finally:
        bypass_tool.cleanup()

if __name__ == "__main__":
    main()
