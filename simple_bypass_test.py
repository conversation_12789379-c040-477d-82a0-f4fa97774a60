#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Turnstile 绕过测试脚本
快速测试有头模式绕过效果
"""

from DrissionPage import ChromiumOptions, Chromium
import time
import random

def create_simple_bypass():
    """创建简单的绕过配置"""
    print("🚀 正在初始化简单绕过测试...")
    
    co = ChromiumOptions()
    
    # 基础反检测
    co.set_argument('--no-sandbox')
    co.set_argument('--disable-blink-features=AutomationControlled')
    co.set_argument('--disable-dev-shm-usage')
    
    # 随机窗口大小
    co.set_argument(f'--window-size={random.randint(1200, 1600)},{random.randint(800, 1000)}')
    
    # 设置用户代理
    co.set_user_agent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
    
    # 禁用一些检测特征
    co.set_pref('credentials_enable_service', False)
    co.set_pref('profile.password_manager_enabled', False)
    
    # 自动端口
    co.auto_port()
    
    return Chromium(co)

def test_website_access():
    """测试网站访问"""
    browser = None
    
    try:
        # 初始化浏览器
        browser = create_simple_bypass()
        print("✅ 浏览器初始化成功")
        
        # 访问目标网站
        url = "https://app.augmentcode.com/account"
        print(f"🌐 正在访问: {url}")
        
        browser.get(url)
        
        # 等待页面加载
        time.sleep(3)
        
        # 获取页面信息
        title = browser.title
        current_url = browser.url
        
        print(f"📄 页面标题: {title}")
        print(f"🔗 当前URL: {current_url}")
        
        # 检查是否有Turnstile
        print("\n🔍 检查 Turnstile 验证...")
        
        # 方法1: 查找iframe
        turnstile_iframes = browser.eles('iframe[src*="challenges.cloudflare.com"]')
        if turnstile_iframes:
            print(f"⚠️  发现 {len(turnstile_iframes)} 个 Turnstile iframe")
            for i, iframe in enumerate(turnstile_iframes):
                print(f"   iframe {i+1}: {iframe.attr('src')}")
        
        # 方法2: 查找Turnstile容器
        turnstile_containers = browser.eles('.cf-turnstile')
        if turnstile_containers:
            print(f"⚠️  发现 {len(turnstile_containers)} 个 Turnstile 容器")
        
        # 方法3: 检查脚本加载
        scripts = browser.eles('script[src*="turnstile"]')
        if scripts:
            print(f"⚠️  发现 {len(scripts)} 个 Turnstile 脚本")
            for script in scripts:
                print(f"   脚本: {script.attr('src')}")
        
        if not turnstile_iframes and not turnstile_containers and not scripts:
            print("✅ 未检测到明显的 Turnstile 元素")
        
        # 检查登录表单
        print("\n📝 检查登录表单...")
        email_input = browser.ele('#username', timeout=2)
        if email_input:
            print("✅ 找到邮箱输入框")
            
            # 检查是否可以输入
            try:
                email_input.click()
                time.sleep(0.5)
                email_input.input('<EMAIL>')
                print("✅ 成功输入测试邮箱")
                
                # 查找提交按钮
                submit_btn = browser.ele('button[type="submit"]', timeout=2)
                if submit_btn:
                    print("✅ 找到提交按钮")
                    print("⚠️  建议: 手动点击按钮测试是否触发验证")
                
            except Exception as e:
                print(f"❌ 输入测试失败: {str(e)}")
        else:
            print("❌ 未找到邮箱输入框")
        
        # 执行反检测脚本
        print("\n🛡️  执行反检测脚本...")
        anti_detection_script = '''
        // 移除webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // 清理chrome runtime
        if (window.chrome && window.chrome.runtime) {
            delete window.chrome.runtime.onConnect;
        }
        
        // 随机化鼠标位置
        let screenX = Math.floor(Math.random() * 800) + 400;
        let screenY = Math.floor(Math.random() * 600) + 200;
        
        Object.defineProperty(MouseEvent.prototype, 'screenX', {
            get: function() { return screenX; }
        });
        
        Object.defineProperty(MouseEvent.prototype, 'screenY', {
            get: function() { return screenY; }
        });
        
        console.log('Anti-detection script executed');
        return 'success';
        '''
        
        try:
            result = browser.run_js(anti_detection_script)
            print(f"✅ 反检测脚本执行结果: {result}")
        except Exception as e:
            print(f"❌ 反检测脚本执行失败: {str(e)}")
        
        # 保持浏览器打开供观察
        print("\n" + "="*50)
        print("🔍 浏览器将保持打开供您测试...")
        print("请尝试以下操作:")
        print("1. 在邮箱框中输入邮箱地址")
        print("2. 点击 'Continue' 按钮")
        print("3. 观察是否出现 Turnstile 验证")
        print("4. 如果出现验证，观察是否能自动通过")
        print("5. 按 Ctrl+C 退出程序")
        print("="*50)
        
        # 等待用户操作
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n⚠️  用户中断测试")
    
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        if browser:
            try:
                browser.quit()
                print("✅ 浏览器已关闭")
            except:
                pass

if __name__ == "__main__":
    print("🎯 Simple Turnstile Bypass Test")
    print("=" * 40)
    test_website_access()
